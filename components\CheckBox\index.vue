<template>
  <view class="checkbox-container" @click="toggleCheck">
    <view class="checkbox" :class="{ 'checked': modelValue }">
      <view v-if="modelValue" class="checkmark">✓</view>
    </view>
    <text v-if="$slots.default" class="checkbox-label">
      <slot></slot>
    </text>
  </view>
</template>

<script>
export default {
  name: 'CheckB<PERSON>',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  methods: {
    toggleCheck() {
      if (this.disabled) return
      
      const newValue = !this.modelValue
      this.$emit('update:modelValue', newValue)
      this.$emit('change', newValue)
    }
  }
}
</script>

<style scoped>
.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #ff4e4b;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.checkbox.checked {
  background-color: #ff4e4b;
}

.checkmark {
  color: white;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
}

.checkbox-label {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
  flex: 1;
}
</style>
